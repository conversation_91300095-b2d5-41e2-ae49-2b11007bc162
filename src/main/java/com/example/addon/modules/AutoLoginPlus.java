package com.example.addon.modules;

import com.example.addon.BaseModule;
import com.example.addon.utils.StringMapSetting;
import meteordevelopment.meteorclient.events.packets.PacketEvent;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;

import java.util.LinkedHashMap;
import java.util.Map;

public class AutoLoginPlus extends BaseModule {
    private final SettingGroup sgGeneral = this.settings.getDefaultGroup();

    // 调试模式
    private final Setting<Boolean> debugMode = sgGeneral.add(new BoolSetting.Builder()
        .name("调试模式")
        .description("显示详细的调试信息")
        .defaultValue(false)
        .build()
    );

    // 延迟设置
    private final Setting<Integer> delay = sgGeneral.add(new IntSetting.Builder()
        .name("登录延迟")
        .description("收到登录提示后延迟多少毫秒执行登录命令")
        .defaultValue(500)
        .min(0)
        .sliderMax(5000)
        .build()
    );

    // 账户列表 - 使用StringMapSetting实现真正的键值对列表
    private final Setting<Map<String, String>> accounts = sgGeneral.add(new StringMapSetting.Builder()
        .name("账户列表")
        .description("用户名和密码的键值对列表。键为用户名，值为密码")
        .defaultValue(new LinkedHashMap<>())
        .build()
    );

    public AutoLoginPlus() {
        super(
            "自动登录plus",
            "检测当前MC用户名并自动匹配对应密码登录服务器"
        );
    }

    @Override
    public void onActivate() {
        super.onActivate();
        if (debugMode.get()) {
            info("自动登录模块已激活");
            info("当前用户名: " + getCurrentUsername());

            // 显示所有配置的账户
            for (String username : accounts.get().keySet()) {
                info("已配置账户: " + username);
            }
        }
    }

    /**
     * 获取当前MC客户端的用户名
     */
    private String getCurrentUsername() {
        try {
           return mc.player.getName().getString();
        } catch (Exception e) {
            if (debugMode.get()) {
                error("获取用户名失败: " + e.getMessage());
            }
        }
        return "未知用户";
    }

    /**
     * 根据当前用户名获取对应密码
     */
    private String getPasswordForCurrentUser() {
        String currentUser = getCurrentUsername();
        return accounts.get().get(currentUser);
    }

    @EventHandler
    public void onPacketReceiver(PacketEvent.Receive event) {
        if (event.packet instanceof GameMessageS2CPacket packet
            && packet.content().getString().contains("/login")) {

            String currentUser = getCurrentUsername();
            String password = getPasswordForCurrentUser();

            if (password != null && !password.isEmpty()) {
                // 添加延迟执行
                new Thread(() -> {
                    try {
                        Thread.sleep(delay.get());
                        if (mc.getNetworkHandler() != null) {
                            mc.getNetworkHandler().sendChatCommand("login " + password);
                            if (debugMode.get()) {
                                info("为用户 " + currentUser + " 自动登录");
                            }
                        }
                    } catch (InterruptedException e) {
                        if (debugMode.get()) {
                            error("登录延迟被中断: " + e.getMessage());
                        }
                    }
                }).start();
            } else {
                if (debugMode.get()) {
                    warning("未找到用户 " + currentUser + " 的密码配置");
                    info("当前用户名: " + currentUser);
                    info("可用账户: " + String.join(", ", accounts.get().keySet()));
                }
            }
        }
    }
}
