package com.example.addon;

import baritone.api.BaritoneAPI;
import baritone.api.IBaritone;
import com.example.addon.mixin.IClientWorld;
import meteordevelopment.meteorclient.pathing.NopPathManager;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.systems.modules.Category;
import meteordevelopment.meteorclient.systems.modules.Module;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.PendingUpdateManager;
import net.minecraft.client.network.SequencedPacketCreator;

public abstract class BaseModule extends Module {

    protected MinecraftClient mc;

    IBaritone baritone;

    public BaseModule(Category category, String name, String description, String... aliases) {
        super(category, name, description, aliases);
        mc = MinecraftClient.getInstance();
    }

    public BaseModule(Category category, String name, String desc) {
        super(category, name, desc);
        mc = MinecraftClient.getInstance();


        try {
            if (PathManagers.get() instanceof NopPathManager) {
                //noop
            } else {
                baritone = BaritoneAPI.getProvider().getPrimaryBaritone();
            }
        } catch (Exception e) {
            error("请安装Baritone!");
        }
    }

    public BaseModule(String name, String desc) {
        super(AddonTemplate.MyCategory, name, desc);
    }

    @Override
    public void onActivate() {
        super.onActivate();
        if (mc == null) {
            mc = MinecraftClient.getInstance();
        }
    }

    public static void sendSequencedPacket(SequencedPacketCreator packetCreator) {
        MinecraftClient mc = MinecraftClient.getInstance();
        if (mc.getNetworkHandler() == null || mc.world == null) return;
        try (PendingUpdateManager pendingUpdateManager = ((IClientWorld) mc.world).getPendingManager().incrementSequence()) {
            int i = pendingUpdateManager.getSequence();
            mc.getNetworkHandler().sendPacket(packetCreator.predict(i));
        }
    }

}
