package com.example.addon.modules;

import com.example.addon.BaseModule;
import com.example.addon.utils.ConstantPoolDumper;
import meteordevelopment.meteorclient.settings.Setting;
import meteordevelopment.meteorclient.settings.SettingGroup;
import meteordevelopment.meteorclient.settings.StringSetting;

public class G<PERSON><PERSON>eader extends BaseModule {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    // Settings for dumpConstantPool parameters
    private final Setting<String> basePackage = sgGeneral.add(new StringSetting.Builder()
        .name("base-package")
        .description("The base package to scan for ConstantPool classes.")
        .defaultValue("meteordevelopment.meteorclient")
        .build()
    );

    private final Setting<String> outputPath = sgGeneral.add(new StringSetting.Builder()
        .name("output-path")
        .description("The output path for the JSON file.")
        .defaultValue("D:/map.json")
        .build()
    );

    public GGBReader() {
        super("GGBReader", "read");
    }

    @Override
    public void onActivate() {
//        BlockUtilGrimConstantsReader.read();
        ConstantPoolDumper.dumpConstantPool(outputPath.get(), "meteordevelopment",
            "io.netty.handler",
            "de.florianmichael",
            "javassist",
            "org.reflections",
            "javax.annotation");
        this.toggle();
    }

}
