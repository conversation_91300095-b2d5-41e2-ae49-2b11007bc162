# AutoLogin 多账户功能使用说明

## 功能概述

AutoLogin模块已升级为支持多账户自动登录功能。模块会自动检测当前MC客户端登录的用户名，并根据预设的账户列表自动匹配对应的密码进行登录。

## 主要特性

1. **自动用户名检测** - 自动检测当前MC客户端的用户名
2. **真正的列表支持** - 使用StringMapSetting实现键值对列表管理
3. **自动匹配** - 根据当前用户名自动选择对应密码
4. **延迟登录** - 可配置登录延迟时间
5. **调试模式** - 提供详细的调试信息帮助排查问题

## 配置方法

### 1. 基本设置

在Meteor客户端中找到"自动登录"模块，你会看到以下设置选项：

- **调试模式**: 开启后会显示详细的调试信息
- **登录延迟**: 收到登录提示后延迟多少毫秒执行登录命令（默认500ms）
- **账户列表**: 用户名和密码的键值对列表

### 2. 账户列表配置

**账户列表**是一个真正的键值对列表设置，支持动态添加和删除账户：

**配置步骤：**
1. 点击"账户列表"设置
2. 在弹出的列表编辑器中：
   - 点击"+"按钮添加新账户
   - 在"键"字段输入MC用户名
   - 在"值"字段输入对应的密码
   - 点击"✓"确认添加
3. 重复步骤2添加更多账户
4. 使用"×"按钮删除不需要的账户

**示例配置：**
```
键(用户名)     值(密码)
Steve         mypassword123
Alex          alexpass456
Notch         notchsecret789
```

**优势：**
- 支持无限数量的账户
- 可以随时添加、编辑、删除账户
- 直观的图形界面操作
- 自动保存配置

### 3. 调试模式

建议在首次配置时开启调试模式，这样可以看到：
- 当前检测到的用户名
- 已配置的账户数量
- 登录时的详细信息
- 错误信息（如果有）

## 工作原理

1. **激活时**: 模块会检测当前用户名并解析账户列表
2. **收到登录提示时**: 
   - 检测当前用户名
   - 在账户列表中查找匹配的密码
   - 如果找到匹配的密码，自动执行登录命令
   - 如果未找到匹配的账户，在调试模式下会显示警告

## 使用步骤

1. 打开Meteor客户端设置
2. 找到"自动登录"模块
3. 开启"调试模式"（推荐）
4. 配置"登录延迟"（可选，默认500ms）
5. 配置账户列表：
   - 点击"账户列表"设置
   - 点击"+"添加账户
   - 输入用户名和密码
   - 重复添加所需的所有账户
6. 激活模块
7. 进入需要登录的服务器，模块会自动处理登录

## 故障排除

### 问题：模块没有自动登录
**解决方案：**
1. 检查调试模式是否显示了正确的用户名
2. 确认账户列表格式是否正确
3. 确认当前用户名是否在账户列表中

### 问题：显示"未找到用户的密码配置"
**解决方案：**
1. 检查当前用户名是否与账户列表中的用户名完全匹配（区分大小写）
2. 确认账户列表中已正确添加该用户名和密码
3. 在调试模式下查看"可用账户"列表，确认账户已正确加载
4. 检查账户列表设置是否已保存

### 问题：获取用户名失败
**解决方案：**
1. 确保MC客户端已正常登录
2. 重启客户端后重试

## 安全提示

- 密码信息存储在客户端配置中，请确保你的设备安全
- 不要在不信任的环境中使用此功能
- 定期更改密码以确保账户安全

## 更新日志

### v3.0 (当前版本)
- 使用StringMapSetting实现真正的键值对列表
- 支持无限数量的账户配置
- 添加登录延迟设置
- 改进的图形界面操作
- 支持动态添加/删除账户
- 异步登录执行，避免阻塞游戏

### v2.0
- 添加多账户支持（最多5个账户）
- 自动检测当前用户名
- 添加调试模式
- 改进用户界面，使用独立的用户名/密码字段
- 移除单一密码设置，改为列表式账户管理
- 增强错误提示和调试信息
