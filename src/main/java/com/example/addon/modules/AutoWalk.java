package com.example.addon.modules;

import com.example.addon.AddonTemplate;
import meteordevelopment.meteorclient.events.world.TickEvent;
import meteordevelopment.meteorclient.pathing.NopPathManager;
import meteordevelopment.meteorclient.pathing.PathManagers;
import meteordevelopment.meteorclient.settings.*;
import meteordevelopment.meteorclient.systems.modules.Module;
import meteordevelopment.meteorclient.utils.misc.input.Input;
import meteordevelopment.orbit.EventHandler;
import meteordevelopment.orbit.EventPriority;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;

public class AutoWalk extends Module {
    private final SettingGroup sgGeneral = settings.getDefaultGroup();

    protected MinecraftClient mc;

    // Internal variables for slow walk
    private int slowWalkTickCounter = 0;

    private final Setting<Mode> mode = sgGeneral.add(new EnumSetting.Builder<Mode>()
        .name("mode")
        .description("Walking mode.")
        .defaultValue(Mode.Smart)
        .onChanged(mode1 -> {
            if (isActive()) {
                if (mode1 == Mode.Simple) {
                    PathManagers.get().stop();
                } else {
                    createGoal();
                }

                unpress(true);
            }
        })
        .build()
    );

    private final Setting<Direction> direction = sgGeneral.add(new EnumSetting.Builder<Direction>()
        .name("simple-direction")
        .description("The direction to walk in simple mode.")
        .defaultValue(Direction.Forwards)
        .onChanged(direction1 -> {
            if (isActive()) unpress(false);
        })
        .visible(() -> mode.get() == Mode.Simple)
        .build()
    );

    private final Setting<Boolean> sprint = sgGeneral.add(new BoolSetting.Builder()
        .name("sprint")
        .description("Sprint while walking.")
        .defaultValue(false)
        .visible(() -> mode.get() == Mode.Simple)
        .build()
    );

    private final Setting<Boolean> slowWalk = sgGeneral.add(new BoolSetting.Builder()
        .name("slow-walk")
        .description("Enable slow walking mode.")
        .defaultValue(false)
        .visible(() -> mode.get() == Mode.Simple)
        .build()
    );

    private final Setting<Integer> slowWalkSpeed = sgGeneral.add(new IntSetting.Builder()
        .name("slow-walk-speed")
        .description("Slow walk speed in ticks (higher = slower). 20 ticks = 1 second.")
        .defaultValue(5)
        .min(2)
        .max(40)
        .sliderMax(20)
        .visible(() -> mode.get() == Mode.Simple && slowWalk.get())
        .build()
    );

    public AutoWalk() {
        super(AddonTemplate.MyCategory, "自动走路", "自动走路.");
        mc = MinecraftClient.getInstance();
    }

    @Override
    public void onActivate() {
        slowWalkTickCounter = 0;
        if (mode.get() == Mode.Smart) createGoal();
    }

    @Override
    public void onDeactivate() {
        if (mode.get() == Mode.Simple) unpress(true);
        else PathManagers.get().stop();
    }

    @EventHandler(priority = EventPriority.HIGH)
    private void onTick(TickEvent.Pre event) {
        if (mode.get() == Mode.Simple) {
            // Handle slow walk timing
            if (slowWalk.get()) {
                slowWalkTickCounter++;

                // Only move every 'slowWalkSpeed' ticks
                if (slowWalkTickCounter >= slowWalkSpeed.get()) {
                    slowWalkTickCounter = 0;
                    handleMovement();
                } else {
                    // Don't move on other ticks, release all keys
                    unpress(false);
                }
            } else {
                // Normal speed movement
                handleMovement();
            }
        } else {
            if (PathManagers.get() instanceof NopPathManager) {
                info("Smart mode requires Baritone");
                toggle();
            }
        }
    }

    private void handleMovement() {
        // Handle movement direction
        switch (direction.get()) {
            case Forwards -> setPressed(mc.options.forwardKey, true);
            case Backwards -> setPressed(mc.options.backKey, true);
            case Left -> setPressed(mc.options.leftKey, true);
            case Right -> setPressed(mc.options.rightKey, true);
        }

        // Handle sprint
        if (sprint.get()) {
            setPressed(mc.options.sprintKey, sprint.get());
        }
    }

    private void unpress(boolean sprintChange) {
        setPressed(mc.options.forwardKey, false);
        setPressed(mc.options.backKey, false);
        setPressed(mc.options.leftKey, false);
        setPressed(mc.options.rightKey, false);
        if (sprintChange && sprint.get()) {
            setPressed(mc.options.sprintKey, false);
        }
    }

    private void setPressed(KeyBinding key, boolean pressed) {
        key.setPressed(pressed);
        Input.setKeyState(key, pressed);
    }

    private void createGoal() {
        PathManagers.get().moveInDirection(mc.player.getYaw());
    }

    public enum Mode {
        Simple,
        Smart
    }

    public enum Direction {
        Forwards,
        Backwards,
        Left,
        Right
    }
}
